import { defineConfig, loadEnv } from "vite";
import { exclude, include } from "./build/optimize";
import { getPluginsList } from "./build/plugins";
import { __APP_INFO__, alias, pathResolve, root, wrapperEnv } from "./build/utils";

export default defineConfig(async ({ mode }) => {
  const { VITE_COMPRESSION, VITE_PORT, VITE_ROUTER_HISTORY, VITE_APP_VCONSOLE, VITE_ROUTER_QUERY_ENCRYPT } = wrapperEnv(loadEnv(mode, root));
  const base = VITE_ROUTER_HISTORY.split(",")?.[1] || "./";
  return {
    // 部署的基本路径
    base: `${base}`,
    // 项目根目录
    root,
    // 插件配置
    plugins: getPluginsList(VITE_COMPRESSION, VITE_APP_VCONSOLE),
    resolve: {
      // 路径别名配置
      alias,
    },
    css: {
      preprocessorOptions: {
        less: {
          // 全局导入 less 变量
          additionalData: `@use "@/assets/styles/var.less" as var;`,
        },
      },
      // Vite 6 新特性：启用 CSS 预处理器多线程处理，显著提高性能
      // 对于使用大量 SCSS/LESS 的项目，可将性能提升 40%
      preprocessorMaxWorkers: true,
    },
    optimizeDeps: {
      // 需要预构建的依赖
      include,
      // 不需要预构建的依赖
      exclude,
    },
    // 开发服务器配置
    server: {
      // 是否自动打开浏览器
      open: false,
      // 端口号
      port: VITE_PORT,
      // 监听所有网络接口
      host: "0.0.0.0",
      // 本地跨域代理配置
      proxy: {
        "^/api/.*": {
          target: "http://*************:8068",
          // target: 'http://*************:8989',
          changeOrigin: true, // 允许跨域
          rewrite: (path) => path.replace(/^\/api/, "/"),
        },
      },
      // Vite 6 特性：文件预热，提前转换和缓存文件
      // 可减少首次页面加载时间，避免转换瀑布问题
      warmup: {
        clientFiles: ["./index.html", "./src/{views,components}/**/*", "./src/main.js", "./src/App.vue", "./src/router/index.js", "./src/store/index.js"],
      },
    },
    build: {
      // 设置为现代浏览器目标，提高性能和减小体积
      target: "es2020",
      // 是否生成 sourcemap
      sourcemap: false,
      // 增加打包警告阈值，避免大型项目产生过多警告
      chunkSizeWarningLimit: 4000,
      rollupOptions: {
        input: {
          // 入口文件配置
          index: pathResolve("index.html", import.meta.url),
        },
        // 静态资源分类打包配置
        output: {
          chunkFileNames: "js/[name]-[hash].js", // 引入文件名的名称
          entryFileNames: "js/[name]-[hash].js", // 包的入口文件名称
          assetFileNames: "[ext]/[name]-[hash].[ext]", // 资源文件像 字体，图片等
          // 将 node_modules 三方依赖包最小化拆分
          manualChunks(id) {
            if (id.includes("node_modules")) {
              const paths = id.toString().split("node_modules/");
              if (paths[2]) {
                return paths[2].split("/")[0].toString();
              }
              return paths[1].split("/")[0].toString();
            }
          },
        },
      },
    },
    define: {
      // 全局常量替换
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
      _VITE_ROUTER_QUERY_ENCRYPT: VITE_ROUTER_QUERY_ENCRYPT,
    },
  };
});

import storageManager, { STORAGE_TYPE } from './storage.js';

/**
 * 存储管理类使用示例
 */
class StorageExample {
  
  /**
   * 基本使用示例
   */
  async basicUsage() {
    console.log('=== 基本使用示例 ===');
    
    // 1. 使用localStorage存储数据（默认方式）
    await storageManager.setItem('user', { name: '张三', age: 25 });
    const user = await storageManager.getItem('user');
    console.log('localStorage用户数据:', user);
    
    // 2. 使用sessionStorage存储数据
    await storageManager.setItem('token', 'abc123', {
      type: STORAGE_TYPE.SESSION
    });
    const token = await storageManager.getItem('token', {
      type: STORAGE_TYPE.SESSION
    });
    console.log('sessionStorage令牌:', token);
    
    // 3. 使用Cookie存储数据
    await storageManager.setItem('theme', 'dark', {
      type: STORAGE_TYPE.COOKIE
    });
    const theme = await storageManager.getItem('theme', {
      type: STORAGE_TYPE.COOKIE
    });
    console.log('Cookie主题:', theme);
    
    // 4. 使用localForage存储数据
    await storageManager.setItem('settings', { lang: 'zh-CN', notifications: true }, {
      type: STORAGE_TYPE.LOCALFORAGE
    });
    const settings = await storageManager.getItem('settings', {
      type: STORAGE_TYPE.LOCALFORAGE
    });
    console.log('localForage设置:', settings);
  }
  
  /**
   * 过期时间示例
   */
  async expireExample() {
    console.log('=== 过期时间示例 ===');
    
    // 设置5秒后过期的数据
    await storageManager.setItem('tempData', '临时数据', {
      expire: 5000 // 5秒
    });
    
    console.log('立即获取:', await storageManager.getItem('tempData'));
    
    // 等待6秒后再获取
    setTimeout(async () => {
      const expiredData = await storageManager.getItem('tempData');
      console.log('6秒后获取:', expiredData); // 应该返回null
    }, 6000);
    
    // 设置永不过期的数据
    await storageManager.setItem('permanentData', '永久数据', {
      expire: 0 // 永不过期
    });
  }
  
  /**
   * 加密存储示例
   */
  async encryptExample() {
    console.log('=== 加密存储示例 ===');
    
    // 加密存储敏感数据
    const sensitiveData = {
      password: '123456',
      creditCard: '1234-5678-9012-3456'
    };
    
    await storageManager.setItem('sensitive', sensitiveData, {
      encrypt: true,
      type: STORAGE_TYPE.LOCALSTORAGE
    });
    
    // 获取加密数据
    const decryptedData = await storageManager.getItem('sensitive', {
      encrypt: true,
      type: STORAGE_TYPE.LOCALSTORAGE
    });
    
    console.log('解密后的敏感数据:', decryptedData);
    
    // 查看localStorage中的原始加密数据
    const rawData = localStorage.getItem('sensitive');
    console.log('localStorage中的加密数据:', rawData);
  }
  
  /**
   * 综合示例：用户会话管理
   */
  async userSessionExample() {
    console.log('=== 用户会话管理示例 ===');
    
    const userInfo = {
      id: 1001,
      username: 'zhangsan',
      email: '<EMAIL>',
      roles: ['user', 'admin']
    };
    
    // 存储用户信息到localStorage，加密，24小时过期
    await storageManager.setItem('userInfo', userInfo, {
      type: STORAGE_TYPE.LOCALSTORAGE,
      encrypt: true,
      expire: 24 * 60 * 60 * 1000 // 24小时
    });
    
    // 存储访问令牌到sessionStorage，不加密，会话结束时过期
    await storageManager.setItem('accessToken', 'eyJhbGciOiJIUzI1NiIs...', {
      type: STORAGE_TYPE.SESSION,
      encrypt: false
    });
    
    // 存储用户偏好到Cookie，不加密，30天过期
    await storageManager.setItem('userPrefs', { theme: 'light', lang: 'zh' }, {
      type: STORAGE_TYPE.COOKIE,
      encrypt: false,
      expire: 30 * 24 * 60 * 60 * 1000 // 30天
    });
    
    // 获取用户会话数据
    const storedUserInfo = await storageManager.getItem('userInfo', {
      type: STORAGE_TYPE.LOCALSTORAGE,
      encrypt: true
    });
    
    const storedToken = await storageManager.getItem('accessToken', {
      type: STORAGE_TYPE.SESSION
    });
    
    const storedPrefs = await storageManager.getItem('userPrefs', {
      type: STORAGE_TYPE.COOKIE
    });
    
    console.log('用户信息:', storedUserInfo);
    console.log('访问令牌:', storedToken);
    console.log('用户偏好:', storedPrefs);
  }
  
  /**
   * 清理示例
   */
  async cleanupExample() {
    console.log('=== 清理示例 ===');
    
    // 删除特定数据
    await storageManager.removeItem('tempData');
    await storageManager.removeItem('token', { type: STORAGE_TYPE.SESSION });
    
    // 清空特定类型的所有数据
    await storageManager.clear(STORAGE_TYPE.SESSION);
    
    console.log('清理完成');
  }
  
  /**
   * 运行所有示例
   */
  async runAllExamples() {
    await this.basicUsage();
    await this.expireExample();
    await this.encryptExample();
    await this.userSessionExample();
    
    // 延迟执行清理示例
    setTimeout(async () => {
      await this.cleanupExample();
    }, 7000);
  }
}

// 导出示例类
export default StorageExample;

// 如果直接运行此文件，执行所有示例
if (typeof window !== 'undefined') {
  const example = new StorageExample();
  // example.runAllExamples();
}

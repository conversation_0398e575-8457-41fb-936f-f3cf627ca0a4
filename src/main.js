import { createApp } from "vue";
import "./utils/mitt/handlers";
import { setupStore } from "@/store";
import router from "./router";
import App from "./App.vue";
// 全局注册@iconify/vue图标库
/* --------------------------------
Vant 中有个别组件是以函数的形式提供的，
包括 Toast，Dialog，Notify 和 ImagePreview 组件。
在使用函数组件时，unplugin-vue-components
无法自动引入对应的样式，因此需要手动引入样式。
------------------------------------- */
import "vant/es/toast/style";
import "vant/es/dialog/style";
import "vant/es/notify/style";
import "vant/es/image-preview/style";
import "vant/es/nav-bar/style";
// 引入tailwindcss
import "./assets/styles/tailwind.css";
// 引入vant样式
import "./assets/styles/vant.less";
const app = createApp(App);
app.use(router);
setupStore(app);
app.mount("#app");

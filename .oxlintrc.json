{
  "$schema": "./node_modules/oxlint/configuration_schema.json",
  "plugins": ["import", "unicorn", "oxc", "promise"],
  "ignorePatterns": ["node_modules", "dist"],
  "rules": {
    "no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_", // 忽略如 `(_foo) => {}`
        "varsIgnorePattern": "^_", // 忽略如 `const _bar = 1`
        "destructuredArrayIgnorePattern": "^_", // 忽略解构数组中的 `const [_first, second] = arr`
        "caughtErrorsIgnorePattern": "^_" // 忽略捕获的错误
      }
    ],
    "unicorn/prefer-node-protocol": "error",
    "import/namespace": [
      "error",
      {
        "allowComputed": true
      }
    ],
    "import/named": "error"
  },
  "globals": {
    "definePage": "readonly"
  }
}

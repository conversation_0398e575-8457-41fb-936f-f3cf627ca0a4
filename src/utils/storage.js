import localForage from './localforage/index.js';
import crypto from './crypto.js';

/**
 * 存储类型枚举
 */
export const STORAGE_TYPE = {
  COOKIE: 'cookie',
  SESSION: 'session',
  LOCALFORAGE: 'localforage',
  LOCALSTORAGE: 'localStorage'
};

/**
 * 简单的存储管理类
 * 支持四种存储方式：COOKIE、SESSION、localForage、localStorage
 * 支持数据过期时间设置和加密/解密功能
 */
class StorageManager {
  constructor() {
    this.defaultExpire = 24 * 60 * 60 * 1000; // 默认过期时间：24小时
  }

  /**
   * 设置数据
   * @param {string} key - 存储键名
   * @param {any} value - 存储值
   * @param {Object} options - 配置选项
   * @param {string} options.type - 存储类型 (COOKIE|SESSION|LOCALFORAGE|LOCALSTORAGE)
   * @param {number} options.expire - 过期时间(毫秒)，0表示永不过期
   * @param {boolean} options.encrypt - 是否加密，默认false
   */
  async setItem(key, value, options = {}) {
    const {
      type = STORAGE_TYPE.LOCALSTORAGE,
      expire = this.defaultExpire,
      encrypt = false
    } = options;

    // 构造存储数据对象
    const storageData = {
      value: value,
      timestamp: Date.now(),
      expire: expire
    };

    // 序列化数据
    let dataStr = JSON.stringify(storageData);

    // 加密处理
    if (encrypt) {
      dataStr = crypto.encrypt(dataStr);
    }

    // 根据存储类型进行存储
    switch (type) {
      case STORAGE_TYPE.COOKIE:
        this._setCookie(key, dataStr, expire);
        break;
      case STORAGE_TYPE.SESSION:
        sessionStorage.setItem(key, dataStr);
        break;
      case STORAGE_TYPE.LOCALFORAGE:
        await localForage.setItem(key, dataStr);
        break;
      case STORAGE_TYPE.LOCALSTORAGE:
      default:
        localStorage.setItem(key, dataStr);
        break;
    }
  }

  /**
   * 获取数据
   * @param {string} key - 存储键名
   * @param {Object} options - 配置选项
   * @param {string} options.type - 存储类型
   * @param {boolean} options.encrypt - 是否加密，默认false
   * @returns {any} 存储的值，如果不存在或已过期返回null
   */
  async getItem(key, options = {}) {
    const {
      type = STORAGE_TYPE.LOCALSTORAGE,
      encrypt = false
    } = options;

    let dataStr = null;

    // 根据存储类型获取数据
    switch (type) {
      case STORAGE_TYPE.COOKIE:
        dataStr = this._getCookie(key);
        break;
      case STORAGE_TYPE.SESSION:
        dataStr = sessionStorage.getItem(key);
        break;
      case STORAGE_TYPE.LOCALFORAGE:
        dataStr = await localForage.getItem(key);
        break;
      case STORAGE_TYPE.LOCALSTORAGE:
      default:
        dataStr = localStorage.getItem(key);
        break;
    }

    if (!dataStr) {
      return null;
    }

    try {
      // 解密处理
      if (encrypt) {
        dataStr = crypto.decrypt(dataStr);
      }

      // 解析数据
      const storageData = JSON.parse(dataStr);
      
      // 检查是否过期
      if (storageData.expire > 0 && 
          Date.now() - storageData.timestamp > storageData.expire) {
        // 数据已过期，删除并返回null
        await this.removeItem(key, { type });
        return null;
      }

      return storageData.value;
    } catch (error) {
      console.error('获取存储数据失败:', error);
      return null;
    }
  }

  /**
   * 删除数据
   * @param {string} key - 存储键名
   * @param {Object} options - 配置选项
   * @param {string} options.type - 存储类型
   */
  async removeItem(key, options = {}) {
    const { type = STORAGE_TYPE.LOCALSTORAGE } = options;

    switch (type) {
      case STORAGE_TYPE.COOKIE:
        this._removeCookie(key);
        break;
      case STORAGE_TYPE.SESSION:
        sessionStorage.removeItem(key);
        break;
      case STORAGE_TYPE.LOCALFORAGE:
        await localForage.removeItem(key);
        break;
      case STORAGE_TYPE.LOCALSTORAGE:
      default:
        localStorage.removeItem(key);
        break;
    }
  }

  /**
   * 清空指定类型的所有数据
   * @param {string} type - 存储类型
   */
  async clear(type = STORAGE_TYPE.LOCALSTORAGE) {
    switch (type) {
      case STORAGE_TYPE.COOKIE:
        // Cookie没有直接清空方法，需要逐个删除
        console.warn('Cookie类型不支持批量清空');
        break;
      case STORAGE_TYPE.SESSION:
        sessionStorage.clear();
        break;
      case STORAGE_TYPE.LOCALFORAGE:
        await localForage.clear();
        break;
      case STORAGE_TYPE.LOCALSTORAGE:
      default:
        localStorage.clear();
        break;
    }
  }

  /**
   * 设置Cookie
   * @private
   */
  _setCookie(key, value, expire) {
    let cookieStr = `${key}=${encodeURIComponent(value)}`;
    
    if (expire > 0) {
      const expireDate = new Date(Date.now() + expire);
      cookieStr += `; expires=${expireDate.toUTCString()}`;
    }
    
    cookieStr += '; path=/';
    document.cookie = cookieStr;
  }

  /**
   * 获取Cookie
   * @private
   */
  _getCookie(key) {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [cookieKey, cookieValue] = cookie.trim().split('=');
      if (cookieKey === key) {
        return decodeURIComponent(cookieValue);
      }
    }
    return null;
  }

  /**
   * 删除Cookie
   * @private
   */
  _removeCookie(key) {
    document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`;
  }
}

// 导出单例实例
export default new StorageManager();

{"name": "vite-vue-h5", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=20.19.0", "pnpm": ">=9.0.0"}, "scripts": {"dev": "vite", "build": "rimraf dist && vite build", "preview": "vite preview"}, "dependencies": {"crypto-js": "^4.2.0", "dayjs": "^1.11.15", "js-base64": "^3.7.8", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "localforage": "^1.10.0", "mitt": "^3.0.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "qs": "^6.14.0", "secure-ls": "^2.0.0", "sm-crypto": "^0.3.13", "vant": "^4.9.21", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"@prettier/plugin-oxc": "^0.0.4", "@tailwindcss/vite": "latest", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.1", "autoprefixer": "^10.4.21", "boxen": "^8.0.1", "gradient-string": "^3.0.0", "less": "^4.4.1", "oxlint": "^1.13.0", "postcss": "^8.5.6", "postcss-mobile-forever": "^5.0.0", "prettier": "^3.6.2", "rimraf": "^6.0.1", "tailwindcss": "^4.1.12", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "unplugin-vue-router": "^0.15.0", "vite": "^7.1.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-vconsole": "^2.1.1", "vite-plugin-vue-layouts-next": "^1.0.0"}}
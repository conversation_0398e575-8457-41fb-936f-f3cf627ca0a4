import { setupLayouts } from "virtual:generated-layouts";
import { createRouter } from "vue-router";
// oxlint-disable-next-line import/named
import { handleHotUpdate, routes } from "vue-router/auto-routes";
import { getHistoryMode } from "./utils";
console.log(routes);
// 设置布局的路由
const _routes = setupLayouts(routes);

// 环境变量
const { VITE_ROUTER_HISTORY } = import.meta.env;

/**
 * 创建路由实例
 */
const router = createRouter({
  history: getHistoryMode(VITE_ROUTER_HISTORY),
  routes: _routes,
  strict: true,
});

// 热更新时，更新路由配置
if (import.meta.hot) {
  handleHotUpdate(router);
}

export default router;

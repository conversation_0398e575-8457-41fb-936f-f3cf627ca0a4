{
  "compilerOptions": {
    "target": "ESNext", // 指定 ECMAScript 目标版本，ESNext 表示最新版本
    "module": "ESNext", // 指定生成的模块代码，ESNext 表示最新的模块系统
    "baseUrl": ".", // 解析非相对模块名的基准目录
    "checkJs": false, // 不检查 JavaScript 文件
    "noImplicitAny": false, // 允许隐式 any 类型
    "noImplicitReturns": false, // 不要求函数中所有分支都有返回值
    "noImplicitThis": false, // 允许 this 上下文隐式推断
    "strict": false, // 关闭所有严格模式检查
    "strictNullChecks": false, // 关闭严格的 null 检查
    "strictFunctionTypes": false, // 关闭严格的函数类型检查
    "strictBindCallApply": false, // 关闭严格的 bind/call/apply 检查
    "strictPropertyInitialization": false, // 关闭严格的属性初始化检查
    "noImplicitOverride": false, // 不要求显式 override 关键字
    "skipLibCheck": true, // 跳过库文件的类型检查
    "paths": {
      // 模块名到基于 baseUrl 的路径映射列表
      "@/*": ["src/*"], // @ 符号映射到 src 目录
      "@build/*": ["build/*"] // @build 符号映射到 build 目录
    }
  }
}

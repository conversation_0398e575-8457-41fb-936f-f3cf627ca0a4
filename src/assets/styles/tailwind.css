@import 'tailwindcss';

@theme {
  --font-pingfang: PingFangSC, 'PingFang SC';
  --color-dark: #222;
}

@layer base {
  h1 {
    font-size: var(--text-2xl);
  }
  h2 {
    font-size: var(--text-xl);
  }
}

@layer components {
  .dark-bg-color {
    background-color: #1c1c1c;
  }
}

@custom-variant dark (&:where(.van-theme-dark, .van-theme-dark *));

@utility flex-c {
  @apply flex justify-center items-center;
}

@utility flex-ac {
  @apply flex justify-around items-center;
}

@utility flex-bc {
  @apply flex justify-between items-center;
}

import localforage from "localforage";

class LocalForage {
  constructor() {
    this.db = localforage.createInstance({
      name: "myApp",
      driver: [localforage.INDEXEDDB, localforage.LOCALSTORAGE],
      version: 1.0,
      storeName: "myApp",
      description: "myApp",
    });
    this.db.ready().then(() => {
      console.log("localforage已准备好");
    });
  }

  async setItem(key, value) {
    return await this.db.setItem(key, value);
  }

  async getItem(key) {
    return await this.db.getItem(key);
  }

  async removeItem(key) {
    return await this.db.removeItem(key);
  }

  async clear() {
    return await this.db.clear();
  }
}

export default new LocalForage();

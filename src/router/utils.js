import { createWebHashHistory, createWebHistory } from "vue-router";

/**
 * 获取路由历史模式
 * @param {string} routerHistory - 历史模式配置，格式为'mode,base'
 * @returns {Function} - 返回对应的history对象
 */
function getHistoryMode(routerHistory) {
  const [mode, base = ""] = routerHistory.split(",");
  if (mode === "hash") {
    return createWebHashHistory(base);
  } else if (mode === "h5") {
    return createWebHistory(base);
  }

  return createWebHashHistory(""); // 默认返回hash模式
}

export { getHistoryMode };

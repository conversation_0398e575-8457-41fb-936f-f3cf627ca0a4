# 增强版本地存储类 (EnhancedStore) 使用文档

一个功能强大、性能优化的前端数据存储解决方案，支持多种存储类型、数据加密、过期管理和智能缓存。

## 🚀 功能概述

### 核心特性

- **🔄 多存储支持**：支持 IndexedDB (localForage)、localStorage、sessionStorage、Cookie 四种存储类型
- **🔐 数据加密**：基于 AES 算法的数据加密保护，确保敏感数据安全
- **⏰ 过期管理**：灵活的过期时间设置和自动清理机制
- **📦 数据压缩**：大数据自动压缩，节省存储空间
- **⚡ 性能优化**：智能缓存、批量操作、异步处理等多重性能优化
- **🔍 性能监控**：内置性能指标统计（命中率、错误率等）
- **🛡️ 错误恢复**：完善的错误处理和降级机制
- **📊 存储分析**：详细的存储使用情况分析
- **🧹 自动清理**：过期数据自动清理和手动清理功能
- **📝 批量操作**：高性能的批量设置和获取数据
- **🍪 Cookie 增强**：完整的 Cookie 存储支持，包括安全选项配置

### 技术亮点

- **ES6+ 语法**：使用现代 JavaScript 语法特性
- **性能优化**：智能缓存、批量处理、异步操作优化
- **TypeScript 友好**：完整的 JSDoc 注释支持
- **向后兼容**：保持与旧版本 API 的兼容性
- **安全第一**：默认启用加密，支持 Cookie 安全选项
- **内存管理**：智能缓存清理，避免内存泄漏

## 📦 快速开始

### 安装依赖

确保项目中已安装以下依赖：

```json
{
  "dependencies": {
    "localforage": "^1.10.0",
    "crypto-js": "^4.2.0",
    "js-cookie": "^3.0.5"
  }
}
```

### 导入和基础使用

```javascript
// 默认导入（推荐）
import store from '@/utils/localforage/store';
import { STORAGE_TYPES } from '@/utils/localforage/store';

// 基本使用示例
async function basicExample() {
  // 存储数据
  await store.setItem('user', { 
    name: 'John Doe', 
    email: '<EMAIL>' 
  });
  
  // 获取数据
  const user = await store.getItem('user');
  console.log(user); // { name: 'John Doe', email: '<EMAIL>' }
  
  // 删除数据
  await store.removeItem('user');
}
```

### 创建自定义实例

```javascript
import { EnhancedStore } from '@/utils/localforage/store';

// 创建自定义配置的实例
const customStore = new EnhancedStore({
  enableEncryption: false,    // 禁用加密
  defaultExpire: 3600,        // 默认1小时过期
  enableCompression: true,    // 启用压缩
  cookieOptions: {
    secure: true,             // 仅 HTTPS
    sameSite: 'Strict'        // 严格同站策略
  }
});
```

## 📚 API 参考

### 存储类型枚举

```javascript
const STORAGE_TYPES = {
  LOCALFORAGE: "localForage",  // IndexedDB/WebSQL (推荐)
  LOCAL: "local",              // localStorage
  SESSION: "session",          // sessionStorage
  COOKIE: "cookie"             // Cookie
};
```

### 核心方法

#### `setItem(key, value, options)`

存储数据到指定的存储类型。

**参数：**
- `key` (string): 存储键，必须为非空字符串
- `value` (any): 要存储的数据，支持任意 JSON 可序列化的数据
- `options` (Object): 配置选项

**选项参数：**
```javascript
{
  type: 'localForage',        // 存储类型
  expire: 0,                  // 过期时间（秒），0表示永不过期
  encrypt: true,              // 是否加密
  compress: false,            // 是否压缩
  cookieOptions: {            // Cookie 特定选项
    secure: false,
    sameSite: 'Lax',
    path: '/',
    domain: undefined
  }
}
```

**返回值：** `Promise<boolean>` - 是否存储成功

**使用示例：**
```javascript
// 基本存储
await store.setItem('config', { theme: 'dark' });

// 带过期时间的存储
await store.setItem('session', { token: 'abc123' }, {
  expire: 3600 // 1小时后过期
});

// 存储到特定类型
await store.setItem('preferences', { lang: 'zh-CN' }, {
  type: STORAGE_TYPES.LOCAL,
  expire: 86400 // 24小时
});

// Cookie 存储（带安全选项）
await store.setItem('auth_token', 'secure_token', {
  type: STORAGE_TYPES.COOKIE,
  expire: 1800, // 30分钟
  cookieOptions: {
    secure: true,
    sameSite: 'Strict',
    path: '/admin'
  }
});
```

#### `getItem(key, options)`

从存储中获取数据。

**参数：**
- `key` (string): 存储键
- `options` (Object): 配置选项

**选项参数：**
```javascript
{
  defaultValue: null,         // 默认值
  autoCleanExpired: true      // 是否自动清理过期数据
}
```

**返回值：** `Promise<any>` - 存储的数据或默认值

**使用示例：**
```javascript
// 基本获取
const config = await store.getItem('config');

// 带默认值的获取
const theme = await store.getItem('theme', { 
  defaultValue: 'light' 
});

// 禁用自动清理过期数据
const data = await store.getItem('cache', { 
  autoCleanExpired: false 
});
```

#### `removeItem(key)`

删除指定键的数据（从所有存储类型中删除）。

**参数：**
- `key` (string): 存储键

**返回值：** `Promise<boolean>` - 是否删除成功

**使用示例：**
```javascript
await store.removeItem('user_session');
```

#### `clear()`

清空所有存储类型中的数据。

**返回值：** `Promise<boolean>` - 是否清空成功

**使用示例：**
```javascript
await store.clear();
```

### 批量操作方法

#### `setItems(items, options)`

批量设置多个数据。

**参数：**
- `items` (Object): 键值对对象
- `options` (Object): 配置选项（同 setItem）

**返回值：** `Promise<boolean>` - 是否全部设置成功

**使用示例：**
```javascript
// 批量设置配置
await store.setItems({
  'app_config': { version: '1.0.0', debug: false },
  'user_settings': { autoSave: true, theme: 'dark' },
  'feature_flags': { newUI: true, betaFeatures: false }
}, {
  type: STORAGE_TYPES.LOCAL,
  expire: 86400 // 24小时
});
```

#### `getItems(keys, options)`

批量获取多个数据。

**参数：**
- `keys` (Array<string>): 键数组
- `options` (Object): 配置选项（同 getItem）

**返回值：** `Promise<Object>` - 键值对对象

**使用示例：**
```javascript
const configs = await store.getItems([
  'app_config', 
  'user_settings', 
  'feature_flags'
]);
console.log(configs);
// 输出: { app_config: {...}, user_settings: {...}, feature_flags: {...} }
```

### 管理和监控方法

#### `keys(type)`

获取指定存储类型的所有键。

**参数：**
- `type` (string): 存储类型，默认为 'localForage'

**返回值：** `Promise<Array<string>>` - 键数组

**使用示例：**
```javascript
// 获取 localStorage 中的所有键
const localKeys = await store.keys(STORAGE_TYPES.LOCAL);

// 获取 Cookie 中的所有键
const cookieKeys = await store.keys(STORAGE_TYPES.COOKIE);
```

#### `getStorageInfo()`

获取所有存储类型的使用情况。

**返回值：** `Promise<Object>` - 存储信息对象

**使用示例：**
```javascript
const info = await store.getStorageInfo();
console.log('存储使用情况:', info);
/*
输出示例:
{
  localForage: { itemCount: 15, totalSize: 2048576, formattedSize: "2.00 MB" },
  localStorage: { itemCount: 8, totalSize: 51200, formattedSize: "50.00 KB" },
  sessionStorage: { itemCount: 3, totalSize: 1024, formattedSize: "1.00 KB" },
  cookie: { itemCount: 5, totalSize: 2048, formattedSize: "2.00 KB" },
  metrics: { hits: 150, misses: 10, errors: 2 }
}
*/
```

#### `cleanExpired()`

清理所有存储类型中的过期数据。

**返回值：** `Promise<number>` - 清理的数据条数

**使用示例：**
```javascript
const cleanedCount = await store.cleanExpired();
console.log(`清理了 ${cleanedCount} 条过期数据`);
```

## 🗄️ 存储类型详解

### 存储类型对比

| 存储类型 | 容量限制 | 持久性 | 性能 | 适用场景 |
|---------|---------|--------|------|----------|
| **LocalForage** | ~50MB+ | 持久化 | 中等 | 大量数据、离线应用、主要存储 |
| **LocalStorage** | ~5-10MB | 持久化 | 最快 | 配置信息、用户偏好 |
| **SessionStorage** | ~5-10MB | 会话级 | 最快 | 临时数据、表单状态 |
| **Cookie** | ~4KB | 可配置 | 较慢 | 认证令牌、跨域数据 |

### LocalForage (IndexedDB)

**特点：**
- 容量大，支持复杂数据结构
- 异步操作，不阻塞主线程
- 支持事务和索引
- 浏览器兼容性好

**适用场景：**
```javascript
// 大量用户数据
await store.setItem('user_data_cache', {
  users: largeUserArray,
  metadata: { lastSync: Date.now() }
}, {
  type: STORAGE_TYPES.LOCALFORAGE,
  expire: 86400 // 24小时缓存
});

// 离线数据存储
await store.setItem('offline_queue', pendingRequests, {
  type: STORAGE_TYPES.LOCALFORAGE
});
```

### LocalStorage

**特点：**
- 持久化存储，浏览器关闭后仍然存在
- 同步操作，速度快
- 同域名下所有标签页共享
- 容量限制约 5-10MB

**适用场景：**
```javascript
// 应用配置
await store.setItem('app_settings', {
  theme: 'dark',
  language: 'zh-CN',
  autoSave: true
}, {
  type: STORAGE_TYPES.LOCAL
});

// 用户偏好
await store.setItem('user_preferences', {
  notifications: true,
  emailUpdates: false
}, {
  type: STORAGE_TYPES.LOCAL,
  expire: 2592000 // 30天
});
```

### SessionStorage

**特点：**
- 会话级存储，标签页关闭后清除
- 同步操作，速度快
- 不同标签页之间隔离
- 适合临时数据

**适用场景：**
```javascript
// 表单状态保存
await store.setItem('form_draft', {
  title: '草稿标题',
  content: '草稿内容...',
  lastModified: Date.now()
}, {
  type: STORAGE_TYPES.SESSION
});

// 页面状态
await store.setItem('page_state', {
  scrollPosition: 1200,
  activeTab: 'settings',
  filters: { category: 'tech', date: '2024' }
}, {
  type: STORAGE_TYPES.SESSION
});
```

### Cookie

**特点：**
- 自动发送到服务器
- 支持过期时间、域名、路径等配置
- 容量限制约 4KB
- 支持安全选项（Secure、SameSite等）

**适用场景：**
```javascript
// 认证令牌
await store.setItem('auth_token', 'jwt_token_here', {
  type: STORAGE_TYPES.COOKIE,
  expire: 3600, // 1小时
  cookieOptions: {
    secure: true,        // 仅 HTTPS
    sameSite: 'Strict',  // 严格同站策略
    path: '/'            // 全站有效
  }
});

// 用户标识
await store.setItem('user_id', 'user123', {
  type: STORAGE_TYPES.COOKIE,
  expire: 86400, // 24小时
  cookieOptions: {
    sameSite: 'Lax',
    path: '/'
  }
});

// 跨域共享数据
await store.setItem('shared_config', { version: '1.0' }, {
  type: STORAGE_TYPES.COOKIE,
  cookieOptions: {
    domain: '.example.com', // 子域名共享
    path: '/',
    secure: false
  }
});
```

## 🔧 高级功能

### 过期时间管理

增强版存储类提供了灵活的过期时间管理机制：

#### 基本过期设置

```javascript
// 设置过期时间（秒）
await store.setItem('cache_data', data, {
  expire: 3600 // 1小时后过期
});

// 永不过期（默认）
await store.setItem('permanent_data', data, {
  expire: 0 // 或者不设置 expire 参数
});
```

#### 不同时间单位的便捷方法

```javascript
// 分钟
const MINUTES = 60;
await store.setItem('short_cache', data, {
  expire: 30 * MINUTES // 30分钟
});

// 小时
const HOURS = 3600;
await store.setItem('medium_cache', data, {
  expire: 6 * HOURS // 6小时
});

// 天
const DAYS = 86400;
await store.setItem('long_cache', data, {
  expire: 7 * DAYS // 7天
});
```

#### 过期检查和清理

```javascript
// 获取数据时自动检查过期（默认行为）
const data = await store.getItem('cache_data');
if (data === null) {
  console.log('数据已过期或不存在');
}

// 禁用自动清理，手动检查
const dataWithoutCleanup = await store.getItem('cache_data', {
  autoCleanExpired: false
});

// 手动清理所有过期数据
const cleanedCount = await store.cleanExpired();
console.log(`清理了 ${cleanedCount} 条过期数据`);
```

### 数据加密功能

#### 加密配置

```javascript
// 启用加密（默认）
await store.setItem('sensitive_data', {
  password: 'secret123',
  apiKey: 'key_abc123'
}, {
  encrypt: true // 默认值
});

// 禁用加密（提升性能）
await store.setItem('public_data', {
  theme: 'dark',
  language: 'zh-CN'
}, {
  encrypt: false
});
```

#### 加密安全性说明

- **算法**：使用 AES 加密算法
- **密钥管理**：密钥存储在代码中，建议生产环境使用环境变量
- **性能影响**：加密会增加 15-30% 的存储时间，10-20% 的读取时间
- **兼容性**：加密后的数据只能通过相同的密钥解密

```javascript
// 混合使用示例
async function securityExample() {
  // 敏感数据加密存储
  await store.setItem('user_credentials', {
    username: 'john_doe',
    token: 'sensitive_token'
  }, {
    encrypt: true,
    expire: 3600
  });

  // 公开配置不加密
  await store.setItem('ui_config', {
    theme: 'dark',
    sidebar: 'collapsed'
  }, {
    encrypt: false
  });
}
```

### 数据压缩功能

#### 自动压缩

```javascript
// 大数据自动压缩
const largeData = {
  users: new Array(1000).fill(null).map((_, i) => ({
    id: i,
    name: `User ${i}`,
    profile: 'A'.repeat(100) // 大量重复数据
  }))
};

await store.setItem('large_dataset', largeData, {
  compress: true, // 启用压缩
  expire: 3600
});
```

#### 压缩策略

- **阈值**：默认对超过 1KB 的数据启用压缩
- **算法**：使用简单的字符串压缩（可扩展为更高效的算法）
- **性能**：压缩会增加存储时间，但减少存储空间和传输时间

```javascript
// 压缩配置示例
async function compressionExample() {
  // 强制压缩小数据
  await store.setItem('small_but_compressed', smallData, {
    compress: true
  });

  // 禁用大数据压缩（如果需要快速访问）
  await store.setItem('large_but_uncompressed', largeData, {
    compress: false
  });
}
```

## ⚙️ 配置选项

### 默认配置

```javascript
const DEFAULT_CONFIG = {
  // 默认过期时间（秒），0 表示永不过期
  defaultExpire: 0,

  // 是否启用加密
  enableEncryption: true,

  // 是否启用压缩（对于大数据）
  enableCompression: false,

  // 数据版本号，用于数据迁移
  dataVersion: "1.0.0",

  // Cookie 默认配置
  cookieOptions: {
    path: "/",              // Cookie 路径
    domain: undefined,      // Cookie 域名
    secure: false,          // 是否仅 HTTPS
    sameSite: "Lax"        // SameSite 策略
  }
};
```

### 自定义配置

```javascript
import { EnhancedStore } from '@/utils/localforage/store';

// 高安全性配置
const secureStore = new EnhancedStore({
  enableEncryption: true,
  defaultExpire: 3600,        // 默认1小时过期
  cookieOptions: {
    secure: true,             // 仅 HTTPS
    sameSite: 'Strict',       // 严格同站策略
    path: '/secure'           // 限制路径
  }
});

// 高性能配置
const fastStore = new EnhancedStore({
  enableEncryption: false,    // 禁用加密提升性能
  enableCompression: true,    // 启用压缩节省空间
  defaultExpire: 0,           // 永不过期
  cookieOptions: {
    sameSite: 'Lax'
  }
});

// 开发环境配置
const devStore = new EnhancedStore({
  enableEncryption: false,    // 开发时禁用加密便于调试
  defaultExpire: 300,         // 5分钟过期，便于测试
  dataVersion: "dev-1.0.0"
});
```

### Cookie 配置详解

```javascript
// Cookie 安全配置选项
const cookieOptions = {
  // 路径配置
  path: "/",                  // Cookie 有效路径

  // 域名配置
  domain: ".example.com",     // 子域名共享

  // 安全选项
  secure: true,               // 仅 HTTPS 传输

  // SameSite 策略
  sameSite: "Strict",         // "Strict" | "Lax" | "None"

  // 过期时间（由 expire 参数控制，无需手动设置）
  // expires: new Date(...)   // 自动计算
};

// 不同场景的 Cookie 配置
async function cookieConfigExamples() {
  // 认证令牌（高安全）
  await store.setItem('auth_token', token, {
    type: STORAGE_TYPES.COOKIE,
    expire: 1800, // 30分钟
    cookieOptions: {
      secure: true,
      sameSite: 'Strict',
      path: '/'
    }
  });

  // 用户偏好（跨子域）
  await store.setItem('user_prefs', preferences, {
    type: STORAGE_TYPES.COOKIE,
    expire: 2592000, // 30天
    cookieOptions: {
      domain: '.example.com',
      sameSite: 'Lax',
      path: '/'
    }
  });

  // 第三方集成（跨站）
  await store.setItem('third_party_id', id, {
    type: STORAGE_TYPES.COOKIE,
    expire: 86400, // 24小时
    cookieOptions: {
      sameSite: 'None',
      secure: true, // SameSite=None 必须配合 Secure
      path: '/'
    }
  });
}
```

## 🎯 最佳实践

### 性能优化建议

#### 1. 选择合适的存储类型

```javascript
// ✅ 推荐：根据数据特性选择存储类型
async function optimizedStorageSelection() {
  // 大量数据 -> LocalForage
  await store.setItem('large_dataset', bigData, {
    type: STORAGE_TYPES.LOCALFORAGE,
    compress: true
  });

  // 配置信息 -> LocalStorage
  await store.setItem('app_config', config, {
    type: STORAGE_TYPES.LOCAL
  });

  // 临时状态 -> SessionStorage
  await store.setItem('form_state', formData, {
    type: STORAGE_TYPES.SESSION
  });

  // 认证信息 -> Cookie
  await store.setItem('auth_token', token, {
    type: STORAGE_TYPES.COOKIE,
    expire: 3600,
    cookieOptions: { secure: true }
  });
}
```

#### 2. 使用批量操作

```javascript
// ✅ 推荐：批量操作
async function batchOperations() {
  // 批量设置
  await store.setItems({
    'config1': data1,
    'config2': data2,
    'config3': data3
  }, {
    type: STORAGE_TYPES.LOCAL,
    expire: 86400
  });

  // 批量获取
  const results = await store.getItems([
    'config1', 'config2', 'config3'
  ]);
}

// ❌ 避免：逐个操作
async function inefficientApproach() {
  await store.setItem('config1', data1);
  await store.setItem('config2', data2);
  await store.setItem('config3', data3);
}
```

#### 3. 合理设置过期时间

```javascript
// 根据数据特性设置过期时间
async function expirationStrategy() {
  // 用户会话：短期过期
  await store.setItem('user_session', sessionData, {
    expire: 1800 // 30分钟
  });

  // 应用配置：中期过期
  await store.setItem('app_config', configData, {
    expire: 86400 // 24小时
  });

  // 缓存数据：长期过期
  await store.setItem('static_cache', cacheData, {
    expire: 604800 // 7天
  });

  // 永久数据：不设置过期
  await store.setItem('user_preferences', preferences);
}
```

#### 4. 智能缓存策略

```javascript
// 实现多级缓存
class SmartCache {
  constructor() {
    this.memoryCache = new Map();
    this.maxMemoryItems = 50;
  }

  async get(key) {
    // 1. 检查内存缓存
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }

    // 2. 从持久化存储获取
    const data = await store.getItem(key);
    if (data) {
      // 更新内存缓存
      this._updateMemoryCache(key, data);
    }

    return data;
  }

  async set(key, value, options) {
    // 同时更新内存缓存和持久化存储
    this._updateMemoryCache(key, value);
    return await store.setItem(key, value, options);
  }

  _updateMemoryCache(key, value) {
    if (this.memoryCache.size >= this.maxMemoryItems) {
      // 删除最旧的项
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }
    this.memoryCache.set(key, value);
  }
}
```

### 错误处理最佳实践

#### 1. 基础错误处理

```javascript
async function robustStorageOperations() {
  try {
    // 尝试存储数据
    const success = await store.setItem('important_data', data);

    if (!success) {
      console.warn('数据存储失败，但程序可以继续运行');
      // 实施降级策略
      await fallbackStorage(data);
    }

  } catch (error) {
    console.error('存储操作出错:', error);

    // 根据错误类型处理
    if (error.name === 'QuotaExceededError') {
      await handleQuotaExceeded();
    } else if (error.name === 'SecurityError') {
      await handleSecurityError();
    }
  }
}

async function handleQuotaExceeded() {
  console.warn('存储空间不足，开始清理');

  // 1. 清理过期数据
  const cleaned = await store.cleanExpired();
  console.log(`清理了 ${cleaned} 条过期数据`);

  // 2. 如果仍然不够，清理最旧的数据
  if (cleaned === 0) {
    await cleanOldestData();
  }
}

async function handleSecurityError() {
  console.warn('存储访问被阻止，可能是隐私模式');
  // 使用内存存储作为降级方案
  useMemoryFallback();
}
```

#### 2. 数据验证和恢复

```javascript
async function dataValidationExample() {
  // 存储时添加校验信息
  const userData = {
    name: 'John',
    email: '<EMAIL>',
    checksum: calculateChecksum(data) // 数据校验和
  };

  await store.setItem('user_data', userData, {
    expire: 3600
  });

  // 获取时验证数据完整性
  const retrievedData = await store.getItem('user_data');

  if (retrievedData && validateData(retrievedData)) {
    console.log('数据验证通过');
    return retrievedData;
  } else {
    console.warn('数据验证失败，重新获取');
    // 从服务器重新获取数据
    const freshData = await fetchFromServer();
    await store.setItem('user_data', freshData);
    return freshData;
  }
}

function validateData(data) {
  // 实施数据验证逻辑
  return data &&
         data.name &&
         data.email &&
         data.checksum === calculateChecksum(data);
}
```

### 安全考虑

#### 1. 敏感数据处理

```javascript
// 敏感数据分类存储
async function secureDataHandling() {
  // 高敏感数据：加密 + 短期过期
  await store.setItem('payment_info', paymentData, {
    encrypt: true,
    expire: 900, // 15分钟
    type: STORAGE_TYPES.SESSION // 会话级存储
  });

  // 中敏感数据：加密 + 中期过期
  await store.setItem('user_profile', profileData, {
    encrypt: true,
    expire: 86400, // 24小时
    type: STORAGE_TYPES.LOCAL
  });

  // 低敏感数据：可选加密 + 长期存储
  await store.setItem('ui_preferences', uiData, {
    encrypt: false, // 性能优先
    expire: 2592000, // 30天
    type: STORAGE_TYPES.LOCAL
  });
}
```

#### 2. Cookie 安全配置

```javascript
// 不同安全级别的 Cookie 配置
async function secureCookieExamples() {
  // 最高安全级别
  await store.setItem('csrf_token', token, {
    type: STORAGE_TYPES.COOKIE,
    expire: 3600,
    cookieOptions: {
      secure: true,      // 仅 HTTPS
      sameSite: 'Strict', // 最严格的同站策略
      path: '/api'       // 限制路径
    }
  });

  // 中等安全级别
  await store.setItem('session_id', sessionId, {
    type: STORAGE_TYPES.COOKIE,
    expire: 7200,
    cookieOptions: {
      secure: true,
      sameSite: 'Lax',   // 允许部分跨站请求
      path: '/'
    }
  });

  // 基础安全级别
  await store.setItem('user_prefs', preferences, {
    type: STORAGE_TYPES.COOKIE,
    expire: 2592000,
    cookieOptions: {
      secure: false,     // 兼容 HTTP
      sameSite: 'Lax',
      path: '/'
    }
  });
}
```

## ❓ 常见问题解答

### Q1: 如何选择合适的存储类型？

**A:** 根据数据特性和使用场景选择：

- **LocalForage (IndexedDB)**: 大量数据、复杂对象、离线应用
- **LocalStorage**: 配置信息、用户偏好、持久化小数据
- **SessionStorage**: 临时状态、表单数据、会话级数据
- **Cookie**: 认证令牌、跨域数据、需要发送到服务器的数据

```javascript
// 选择指南
const storageGuide = {
  // 数据量大 (>1MB)
  largeData: STORAGE_TYPES.LOCALFORAGE,

  // 需要持久化的配置
  config: STORAGE_TYPES.LOCAL,

  // 临时会话数据
  temporary: STORAGE_TYPES.SESSION,

  // 需要发送到服务器
  serverSync: STORAGE_TYPES.COOKIE
};
```

### Q2: 数据加密会影响性能吗？

**A:** 会有一定影响，但通常可以接受：

- **写入性能**: 增加 15-30% 的时间
- **读取性能**: 增加 10-20% 的时间
- **存储空间**: 增加约 30-40% 的空间占用

```javascript
// 性能测试示例
async function performanceComparison() {
  const testData = { large: 'data'.repeat(1000) };

  // 测试加密存储
  console.time('encrypted');
  await store.setItem('test_encrypted', testData, { encrypt: true });
  console.timeEnd('encrypted');

  // 测试非加密存储
  console.time('unencrypted');
  await store.setItem('test_unencrypted', testData, { encrypt: false });
  console.timeEnd('unencrypted');
}
```

**建议**: 对敏感数据启用加密，对性能要求极高的场景可以考虑禁用。

### Q3: Cookie 存储有什么限制？

**A:** Cookie 存储有以下限制：

- **大小限制**: 单个 Cookie 最大 4KB
- **数量限制**: 每个域名最多 50-180 个 Cookie（浏览器差异）
- **自动发送**: Cookie 会在每次 HTTP 请求中发送

```javascript
// Cookie 使用建议
async function cookieBestPractices() {
  // ✅ 适合存储到 Cookie
  await store.setItem('auth_token', 'short_token', {
    type: STORAGE_TYPES.COOKIE,
    expire: 3600,
    cookieOptions: { secure: true, sameSite: 'Strict' }
  });

  // ❌ 不适合存储到 Cookie（数据太大）
  // await store.setItem('large_data', largeObject, {
  //   type: STORAGE_TYPES.COOKIE
  // });
}
```

### Q4: 如何处理存储空间不足的问题？

**A:** 实施以下策略：

```javascript
async function handleQuotaExceeded() {
  try {
    await store.setItem('new_data', data);
  } catch (error) {
    if (error.name === 'QuotaExceededError') {
      // 1. 清理过期数据
      const cleaned = await store.cleanExpired();

      if (cleaned > 0) {
        // 重试存储
        await store.setItem('new_data', data);
      } else {
        // 2. 清理最旧的数据或迁移到其他存储
        await cleanOldestData();
        // 3. 考虑使用压缩
        await store.setItem('new_data', data, { compress: true });
      }
    }
  }
}

async function cleanOldestData() {
  const info = await store.getStorageInfo();
  if (info.localStorage.itemCount > 50) {
    // 清理一些非关键数据
    await store.removeItem('non_critical_cache');
  }
}
```

### Q5: 如何实现数据迁移？

**A:** 使用版本号进行数据迁移：

```javascript
async function dataMigration() {
  const currentVersion = '2.0.0';
  const storedVersion = await store.getItem('data_version') || '1.0.0';

  if (storedVersion !== currentVersion) {
    console.log(`数据迁移: ${storedVersion} -> ${currentVersion}`);

    // 执行迁移逻辑
    await migrateData(storedVersion, currentVersion);

    // 更新版本号
    await store.setItem('data_version', currentVersion);
  }
}

async function migrateData(fromVersion, toVersion) {
  if (fromVersion === '1.0.0' && toVersion === '2.0.0') {
    // 具体的迁移逻辑
    const oldData = await store.getItem('old_format_data');
    if (oldData) {
      const newData = transformDataFormat(oldData);
      await store.setItem('new_format_data', newData);
      await store.removeItem('old_format_data');
    }
  }
}

function transformDataFormat(oldData) {
  // 数据格式转换逻辑
  return {
    ...oldData,
    version: '2.0.0',
    newField: 'default_value'
  };
}
```

### Q6: 如何在多个标签页之间同步数据？

**A:** 使用 Storage 事件监听：

```javascript
// 监听存储变化
window.addEventListener('storage', (event) => {
  if (event.key && event.newValue) {
    console.log(`存储更新: ${event.key}`, event.newValue);
    // 更新应用状态
    updateAppState(event.key, event.newValue);
  }
});

// 跨标签页数据同步
async function syncAcrossTabs() {
  await store.setItem('shared_data', {
    timestamp: Date.now(),
    message: 'Hello from another tab!'
  }, {
    type: STORAGE_TYPES.LOCAL // 使用 localStorage 触发 storage 事件
  });
}

function updateAppState(key, value) {
  // 根据键值更新应用状态
  switch (key) {
    case 'user_preferences':
      updateUserPreferences(JSON.parse(value));
      break;
    case 'theme_settings':
      updateTheme(JSON.parse(value));
      break;
  }
}
```

### Q7: 如何监控存储性能？

**A:** 使用内置的性能监控功能：

```javascript
// 获取性能指标
async function monitorPerformance() {
  const info = await store.getStorageInfo();

  console.log('性能指标:', info.metrics);
  // 输出: { hits: 150, misses: 10, errors: 2 }

  // 计算命中率
  const hitRate = (info.metrics.hits / (info.metrics.hits + info.metrics.misses) * 100).toFixed(2);
  console.log(`缓存命中率: ${hitRate}%`);

  // 监控存储使用情况
  console.table({
    'LocalForage': info.localForage,
    'LocalStorage': info.localStorage,
    'SessionStorage': info.sessionStorage,
    'Cookie': info.cookie
  });
}

// 定期监控
setInterval(async () => {
  await monitorPerformance();
}, 60000); // 每分钟监控一次
```

## 📝 总结

增强版本地存储类提供了一个完整、安全、高性能的前端数据存储解决方案。通过合理使用不同的存储类型、配置选项和优化策略，可以显著提升应用的数据管理能力和用户体验。

### 核心优势

- 🔄 **统一接口**: 四种存储类型使用相同的 API
- 🔐 **安全可靠**: 默认加密保护敏感数据
- ⏰ **智能管理**: 自动过期检查和清理
- ⚡ **性能优化**: 智能缓存、批量处理、异步操作
- 📊 **性能监控**: 内置性能指标和存储分析
- 🛡️ **错误恢复**: 完善的错误处理和降级机制

### 最佳实践总结

1. **根据数据特性选择存储类型**
2. **合理设置过期时间**
3. **对敏感数据启用加密**
4. **使用批量操作提升性能**
5. **定期清理过期数据**
6. **实施完善的错误处理**
7. **监控存储使用情况**
8. **考虑数据迁移策略**

### 快速参考

```javascript
// 基本使用
import store, { STORAGE_TYPES } from '@/utils/localforage/store';

// 存储数据
await store.setItem('key', value, { expire: 3600 });

// 获取数据
const data = await store.getItem('key', { defaultValue: null });

// 批量操作
await store.setItems({ key1: value1, key2: value2 });
const results = await store.getItems(['key1', 'key2']);

// 存储管理
const info = await store.getStorageInfo();
const cleaned = await store.cleanExpired();
```

---

**版本**: 1.0.0
**更新时间**: 2024-08-29
**维护者**: 开发团队

如有问题或建议，请联系开发团队或查看项目文档。

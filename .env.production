VITE_NODE_ENV=生产环境

# 页面标题
VITE_TITLE=学情预警

# 是否开启本地菜单
VITE_LOCAL_MENU=true

# 请求地址
VITE_APP_BASE_URL=/blade-api

# 预发布环境路由历史模式（Hash模式传"hash"、HTML5模式传"h5"、Hash模式带base参数传"hash,base参数"、HTML5模式带base参数传"h5,base参数"）
VITE_ROUTER_HISTORY=h5,/yjapp

# 是否启用gzip压缩或brotli压缩（分两种情况，删除原始文件和不删除原始文件）
# 压缩时不删除原始文件的配置：gzip、brotli、both（同时开启 gzip 与 brotli 压缩）、none（不开启压缩，默认）
# 压缩时删除原始文件的配置：gzip-clear、brotli-clear、both-clear（同时开启 gzip 与 brotli 压缩）、none（不开启压缩，默认）
VITE_COMPRESSION=none


# 是否开启vconsole
VITE_APP_VCONSOLE = false
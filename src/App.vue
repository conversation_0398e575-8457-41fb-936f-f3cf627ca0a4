<script setup>
import useAppStore from "@/store/modules/app";
const appStore = useAppStore();
</script>

<template>
  <div class="w-screen h-screen overflow-hidden font-poppins flex-c bg-white dark:bg-dark">
    <van-config-provider :theme="appStore.theme" class="w-screen h-screen overflow-hidden">
      <router-view v-slot="{ Component, route }">
        <component :is="Component" :key="route" />
      </router-view>
    </van-config-provider>
  </div>
</template>

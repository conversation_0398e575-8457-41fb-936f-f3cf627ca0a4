import compressPlugin from 'vite-plugin-compression'

/**
 * 配置压缩插件
 * @param {string} compress - 压缩类型，可选值: 'gzip', 'brotli', 'both', 'none', 'clear'
 * @returns {Array|null} - 返回配置好的压缩插件数组或null
 */
export function configCompressPlugin(compress) {
  // 如果不需要压缩，直接返回null
  if (compress === 'none')
    return null

  // gzip压缩配置
  const gz = {
    // 生成的压缩包后缀
    ext: '.gz',
    // 体积大于threshold才会被压缩
    threshold: 0,
    // 默认压缩.js|mjs|json|css|html后缀文件，设置成true，压缩全部文件
    filter: () => true,
    // 压缩后是否删除原始文件
    deleteOriginFile: false,
  }
  
  // brotli压缩配置
  const br = {
    ext: '.br',
    algorithm: 'brotliCompress',
    threshold: 0,
    filter: () => true,
    deleteOriginFile: false,
  }

  // 压缩类型列表
  const codeList = [
    { k: 'gzip', v: gz },
    { k: 'brotli', v: br },
    { k: 'both', v: [gz, br] }, // both表示同时使用gzip和brotli压缩
  ]

  // 存储最终生成的插件列表
  const plugins = []

  // 遍历压缩类型列表
  codeList.forEach((item) => {
    // 检查是否包含当前压缩类型
    if (compress.includes(item.k)) {
      // 检查是否需要在压缩后删除原始文件
      if (compress.includes('clear')) {
        if (Array.isArray(item.v)) {
          // 处理数组类型的压缩配置（both类型）
          item.v.forEach((vItem) => {
            plugins.push(compressPlugin(Object.assign(vItem, { deleteOriginFile: true })))
          })
        }
        else {
          // 处理单个压缩配置
          plugins.push(compressPlugin(Object.assign(item.v, { deleteOriginFile: true })))
        }
      }
      else {
        // 不删除原始文件的情况
        if (Array.isArray(item.v)) {
          // 处理数组类型的压缩配置
          item.v.forEach((vItem) => {
            plugins.push(compressPlugin(vItem))
          })
        }
        else {
          // 处理单个压缩配置
          plugins.push(compressPlugin(item.v))
        }
      }
    }
  })

  // 返回配置好的插件列表
  return plugins
}
